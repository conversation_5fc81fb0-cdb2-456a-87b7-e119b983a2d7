*{
            margin: 0;
            padding: 0;
            box-sizing: border-box;

}
          

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f2e9 0%, #ede4d0 100%);
            min-height: 100vh;
            color: #5a4a3a;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: bold;
            color: #8b6914;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: #5a4a3a;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #8b6914;
        }

        /* Welcome Page */
        .welcome-page {
            display: block;
            text-align: center;
            padding: 4rem 0;
        }

        .welcome-hero {
            margin-bottom: 3rem;
        }

        .welcome-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #8b6914;
            font-weight: 300;
        }

        .welcome-hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.8;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .login-options {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 2rem;
            min-width: 280px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .login-card h3 {
            color: #8b6914;
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .login-card p {
            margin-bottom: 1.5rem;
            opacity: 0.7;
        }

        .btn {
            background: linear-gradient(135deg, #d4b896 0%, #c4a67a 100%);
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Assistant Page */
        .assistant-page {
            display: none;
        }

        .assistant-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .assistant-header h2 {
            color: #8b6914;
            margin-bottom: 0.5rem;
        }

        .user-type-badge {
            display: inline-block;
            background: #d4b896;
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
        }

        .voice-interface {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .voice-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .mic-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #d4b896 0%, #c4a67a 100%);
            color: white;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }

        .mic-button:hover {
            transform: scale(1.1);
        }

        .mic-button.recording {
            animation: pulse 1.5s infinite;
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .voice-status {
            text-align: center;
            font-size: 1.1rem;
            margin: 1rem 0;
        }

        .language-selector {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .lang-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #d4b896;
            background: transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lang-btn.active {
            background: #d4b896;
            color: white;
        }

        /* Chat Interface */
        .chat-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 1.5rem;
            height: 400px;
            display: flex;
            flex-direction: column;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 10px;
            background: rgba(245, 242, 233, 0.5);
        }

        .message {
            margin-bottom: 1rem;
            padding: 0.8rem;
            border-radius: 15px;
            max-width: 80%;
        }

        .user-message {
            background: #d4b896;
            color: white;
            margin-left: auto;
        }

        .assistant-message {
            background: rgba(255, 255, 255, 0.8);
            color: #5a4a3a;
        }

        .typing-indicator {
            display: none;
            padding: 1rem;
            font-style: italic;
            opacity: 0.7;
        }

        /* Service Cards */
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .service-card {
            background: rgba(255, 255, 255, 0.8);
            padding: 1.5rem;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .service-card:hover {
            transform: translateY(-3px);
        }

        .service-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .welcome-hero h1 {
                font-size: 2rem;
            }
            
            .login-options {
                flex-direction: column;
                align-items: center;
            }
            
            .voice-controls {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-links {
                display: none;
            }
        }

        .hidden {
            display: none !important;
        }

        /* Accessibility features */
        .accessibility-panel {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .accessibility-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .audio-feedback {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #8b6914;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            z-index: 1000;
            transform: translateY(100px);
            transition: transform 0.3s ease;
        }

        .audio-feedback.show {
            transform: translateY(0);
        }