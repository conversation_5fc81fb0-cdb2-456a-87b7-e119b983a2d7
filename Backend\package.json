{"name": "municipal-voice-assistant-backend", "version": "1.0.0", "description": "Backend API for Municipal Voice Assistant with multi-dialect support", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "init-db": "node scripts/initDatabase.js"}, "keywords": ["municipal", "voice-assistant", "api", "speech-recognition", "accessibility"], "author": "Municipal Services Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "node-cron": "^3.0.2", "express-rate-limit": "^6.8.1", "morgan": "^1.10.0", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}