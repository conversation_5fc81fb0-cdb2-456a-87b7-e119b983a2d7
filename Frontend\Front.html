<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Municipal Voice Assistant Portal</title>
   
</head>
<body>
  <link rel="stylesheet" href="style.css">
    <header class="header">
        <div class="container">
            <nav class="nav">
                <div class="logo">🏛️ Municipal AI Assistant</div>
                <ul class="nav-links">
                    <li><a href="#services">Services</a></li>
                    <li><a href="#help">Help</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="container">
        <!-- Welcome Page -->
        <div class="welcome-page" id="welcomePage">
            <div class="welcome-hero">
                <h1>Welcome to Municipal Services Portal</h1>
                <p>Your voice-enabled assistant for all municipal services. Get help with water supply, garbage collection, electricity complaints, and more - all through simple voice commands in your preferred language.</p>
            </div>

            <div class="login-options">
                <div class="login-card" onclick="loginAs('citizen')">
                    <h3>👨‍💼 Login as Citizen</h3>
                    <p>Register complaints, check service status, and access municipal services</p>
                    <button class="btn">Continue as Citizen</button>
                </div>

                <div class="login-card" onclick="loginAs('municipality')">
                    <h3>🏛️ Login as Municipality</h3>
                    <p>Manage citizen requests, update service status, and administrative functions</p>
                    <button class="btn">Continue as Municipality</button>
                </div>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">💧</div>
                    <h4>Water Supply</h4>
                    <p>Report water issues and check supply status</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🗑️</div>
                    <h4>Garbage Collection</h4>
                    <p>Schedule pickups and report missed collections</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">⚡</div>
                    <h4>Electricity</h4>
                    <p>Report power outages and billing issues</p>
                </div>
                <div class="service-card">
                    <div class="service-icon">🛣️</div>
                    <h4>Road Maintenance</h4>
                    <p>Report potholes and road damage</p>
                </div>
            </div>
        </div>

        <!-- Assistant Page -->
        <div class="assistant-page" id="assistantPage">
            <div class="assistant-header">
                <h2>Municipal Voice Assistant</h2>
                <span class="user-type-badge" id="userTypeBadge">Citizen Portal</span>
                <button class="btn" onclick="logout()" style="margin-left: 1rem; padding: 0.5rem 1rem; font-size: 0.9rem;">Logout</button>
            </div>

            <!-- Accessibility Panel -->
            <div class="accessibility-panel">
                <h3 style="text-align: center; margin-bottom: 1rem;">🎧 Accessibility Features</h3>
                <div class="accessibility-controls">
                    <button class="btn" onclick="toggleAudioFeedback()">🔊 Audio Feedback</button>
                    <button class="btn" onclick="increaseTextSize()">🔍 Larger Text</button>
                    <button class="btn" onclick="toggleHighContrast()">🌗 High Contrast</button>
                </div>
            </div>

            <!-- Voice Interface -->
            <div class="voice-interface">
                <div class="language-selector">
                    <button class="lang-btn active" onclick="selectLanguage('en')">English</button>
                    <button class="lang-btn" onclick="selectLanguage('hi')">हिंदी</button>
                    <button class="lang-btn" onclick="selectLanguage('te')">తెలుగు</button>
                    <button class="lang-btn" onclick="selectLanguage('ta')">தமிழ்</button>
                </div>

                <div class="voice-controls">
                    <button class="mic-button" id="micButton" onclick="toggleRecording()">🎤</button>
                </div>
                
                <div class="voice-status" id="voiceStatus">Click the microphone to start speaking</div>
            </div>

            <!-- Chat Interface -->
            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant-message">
                        Hello! I'm your municipal voice assistant. You can speak to me about water supply, garbage collection, electricity complaints, road maintenance, and more. How can I help you today?
                    </div>
                </div>
                <div class="typing-indicator" id="typingIndicator">Assistant is typing...</div>
            </div>
        </div>
    </div>

    <!-- Audio Feedback Panel -->
    <div class="audio-feedback" id="audioFeedback">
        🔊 Audio feedback is enabled
    </div>

    <script>
        // Global variables
        let currentUser = '';
        let currentLanguage = 'en';
        let isRecording = false;
        let recognition;
        let audioEnabled = true;
        let mockComplaintId = 12345;

        // Initialize Speech Recognition
        if ('webkitSpeechRecognition' in window) {
            recognition = new webkitSpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = true;
            recognition.lang = 'en-US';
        } else if ('SpeechRecognition' in window) {
            recognition = new SpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = true;
            recognition.lang = 'en-US';
        }

        // Language mappings for different dialects
        const languageMap = {
            'en': 'en-US',
            'hi': 'hi-IN',
            'te': 'te-IN',
            'ta': 'ta-IN'
        };

        // Login functions
        function loginAs(userType) {
            currentUser = userType;
            document.getElementById('welcomePage').style.display = 'none';
            document.getElementById('assistantPage').style.display = 'block';
            
            const badge = document.getElementById('userTypeBadge');
            badge.textContent = userType === 'citizen' ? 'Citizen Portal' : 'Municipality Portal';
            
            if (audioEnabled) {
                speak(`Welcome ${userType}! You are now logged into the municipal voice assistant.`);
            }
        }

        function logout() {
            document.getElementById('assistantPage').style.display = 'none';
            document.getElementById('welcomePage').style.display = 'block';
            clearChat();
        }

        // Language selection
        function selectLanguage(lang) {
            currentLanguage = lang;
            if (recognition) {
                recognition.lang = languageMap[lang];
            }
            
            // Update active language button
            document.querySelectorAll('.lang-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            
            if (audioEnabled) {
                speak(`Language changed to ${lang === 'en' ? 'English' : lang === 'hi' ? 'Hindi' : lang === 'te' ? 'Telugu' : 'Tamil'}`);
            }
        }

        // Voice recording functions
        function toggleRecording() {
            if (!recognition) {
                addMessage('assistant', 'Sorry, speech recognition is not supported in your browser.');
                return;
            }

            if (isRecording) {
                stopRecording();
            } else {
                startRecording();
            }
        }

        function startRecording() {
            isRecording = true;
            const micButton = document.getElementById('micButton');
            const voiceStatus = document.getElementById('voiceStatus');
            
            micButton.classList.add('recording');
            voiceStatus.textContent = 'Listening... Speak now';
            
            recognition.start();
            
            if (audioEnabled) {
                speak('I am listening. Please speak your request.');
            }
        }

        function stopRecording() {
            isRecording = false;
            const micButton = document.getElementById('micButton');
            const voiceStatus = document.getElementById('voiceStatus');
            
            micButton.classList.remove('recording');
            voiceStatus.textContent = 'Processing...';
            
            if (recognition) {
                recognition.stop();
            }
        }

        // Speech recognition event handlers
        if (recognition) {
            recognition.onresult = function(event) {
                let transcript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    if (event.results[i].isFinal) {
                        transcript += event.results[i][0].transcript;
                    }
                }
                
                if (transcript.trim()) {
                    addMessage('user', transcript);
                    processVoiceInput(transcript);
                }
            };

            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
                document.getElementById('voiceStatus').textContent = 'Error in voice recognition. Please try again.';
                stopRecording();
            };

            recognition.onend = function() {
                document.getElementById('voiceStatus').textContent = 'Click the microphone to start speaking';
                stopRecording();
            };
        }

        // Process voice input and generate response
        async function processVoiceInput(input) {
            showTyping(true);
            
            // Simulate API processing delay
            await new Promise(resolve => setTimeout(resolve, 1500));
            
            const response = await generateResponse(input.toLowerCase());
            
            showTyping(false);
            addMessage('assistant', response.message);
            
            if (audioEnabled) {
                speak(response.message);
            }
            
            // Handle API calls if needed
            if (response.apiCall) {
                await handleApiCall(response.apiCall);
            }
        }

        // Generate appropriate response based on input
        async function generateResponse(input) {
            // Common municipal service keywords and responses
            const serviceKeywords = {
                water: ['water', 'paani', 'jal', 'supply', 'नल', 'पानी'],
                garbage: ['garbage', 'kachra', 'waste', 'कचरा', 'cleaning', 'collection'],
                electricity: ['light', 'power', 'bijli', 'current', 'बिजली', 'electricity'],
                road: ['road', 'sadak', 'pothole', 'सड़क', 'street', 'path']
            };
            
            // Complaint registration patterns
            if (input.includes('complaint') || input.includes('register') || input.includes('problem') || input.includes('issue')) {
                let serviceType = 'general';
                
                for (let [service, keywords] of Object.entries(serviceKeywords)) {
                    if (keywords.some(keyword => input.includes(keyword))) {
                        serviceType = service;
                        break;
                    }
                }
                
                mockComplaintId++;
                
                return {
                    message: `Your ${serviceType} complaint has been registered successfully. Your complaint ID is #${mockComplaintId}. Our team will address this within 24-48 hours. You will receive updates via SMS.`,
                    apiCall: {
                        type: 'registerComplaint',
                        service: serviceType,
                        complaintId: mockComplaintId
                    }
                };
            }
            
            // Status check patterns
            if (input.includes('status') || input.includes('check') || input.includes('update')) {
                return {
                    message: `Let me check the status of your recent requests. Your last complaint #${mockComplaintId} is currently being processed by our maintenance team. Expected resolution: Tomorrow morning.`,
                    apiCall: {
                        type: 'checkStatus',
                        complaintId: mockComplaintId
                    }
                };
            }
            
            // Service-specific responses
            for (let [service, keywords] of Object.entries(serviceKeywords)) {
                if (keywords.some(keyword => input.includes(keyword))) {
                    switch(service) {
                        case 'water':
                            return { message: 'I can help you with water supply issues. Are you facing low water pressure, no water supply, or water quality problems? Please describe your specific issue.' };
                        case 'garbage':
                            return { message: 'For garbage collection services, I can help you report missed pickups, schedule bulk waste collection, or register complaints about cleanliness. What specific help do you need?' };
                        case 'electricity':
                            return { message: 'I can assist with electricity related issues like power outages, billing problems, or connection requests. Please tell me your specific concern.' };
                        case 'road':
                            return { message: 'For road maintenance, I can help you report potholes, broken streetlights, or damaged pavements. Please provide the location details.' };
                    }
                }
            }
            
            // Default response
            return {
                message: 'I can help you with municipal services including water supply, garbage collection, electricity issues, and road maintenance. You can register complaints, check status, or get information about services. What would you like assistance with?'
            };
        }

        // Handle API calls (mock implementation)
        async function handleApiCall(apiCall) {
            console.log('Making API call:', apiCall);
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Mock API responses
            switch(apiCall.type) {
                case 'registerComplaint':
                    console.log(`Complaint registered: Service=${apiCall.service}, ID=${apiCall.complaintId}`);
                    break;
                case 'checkStatus':
                    console.log(`Status checked for complaint ID: ${apiCall.complaintId}`);
                    break;
            }
        }

        // Chat functions
        function addMessage(sender, message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            messageDiv.textContent = message;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function clearChat() {
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.innerHTML = '<div class="message assistant-message">Hello! I\'m your municipal voice assistant. How can I help you today?</div>';
        }

        function showTyping(show) {
            const typingIndicator = document.getElementById('typingIndicator');
            typingIndicator.style.display = show ? 'block' : 'none';
        }

        // Text-to-Speech function
        function speak(text) {
            if (!audioEnabled) return;
            
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = languageMap[currentLanguage] || 'en-US';
                utterance.rate = 0.9;
                speechSynthesis.speak(utterance);
            }
        }

        // Accessibility functions
        function toggleAudioFeedback() {
            audioEnabled = !audioEnabled;
            const feedback = document.getElementById('audioFeedback');
            
            if (audioEnabled) {
                feedback.textContent = '🔊 Audio feedback is enabled';
                feedback.classList.add('show');
                speak('Audio feedback enabled');
            } else {
                feedback.textContent = '🔇 Audio feedback is disabled';
                feedback.classList.add('show');
            }
            
            setTimeout(() => {
                feedback.classList.remove('show');
            }, 2000);
        }

        function increaseTextSize() {
            document.body.style.fontSize = document.body.style.fontSize === '120%' ? '100%' : '120%';
            if (audioEnabled) {
                speak('Text size adjusted');
            }
        }

        function toggleHighContrast() {
            document.body.classList.toggle('high-contrast');
            if (audioEnabled) {
                speak('High contrast mode toggled');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (audioEnabled) {
                speak('Welcome to the Municipal Voice Assistant Portal. Please choose to login as a citizen or municipality.');
            }
        });
    </script>
</body>
</html>